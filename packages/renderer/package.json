{"name": "@app/renderer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build --base ./", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "type-check": "npx tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@remotion/bundler": "v4.0.272", "@remotion/captions": "4.0.272", "@remotion/cli": "4.0.272", "@remotion/cloudrun": "v4.0.272", "@remotion/google-fonts": "4.0.272", "@remotion/lambda": "4.0.272", "@remotion/player": "v4.0.272", "@remotion/renderer": "v4.0.272", "@remotion/studio": "v4.0.272", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.82.0", "@tanstack/react-table": "^8.21.3", "@types/opentype.js": "^1.3.8", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^11.5.6", "gray-matter": "^4.0.3", "localforage": "^1.10.0", "lodash": "^4.17.21", "lucide-react": "^0.438.0", "nanoid": "^5.1.5", "opentype.js": "^1.3.4", "react": "^19.1.0", "react-color": "^2.19.3", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-hotkeys-hook": "^4.6.1", "react-resizable-panels": "^3.0.3", "react-router": "^7.6.3", "react-router-dom": "^7.6.3", "react-toastify": "^11.0.5", "remotion": "4.0.272", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.22.3", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@stylistic/eslint-plugin": "^5.1.0", "@stylistic/eslint-plugin-ts": "^4.4.1", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/localforage": "^0.0.33", "@types/lodash": "^4.17.20", "@types/react": "^19.1.2", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.30.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.3.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^6.3.5"}, "main": "./dist/index.html", "exports": {".": {"default": "./dist/index.html"}}}