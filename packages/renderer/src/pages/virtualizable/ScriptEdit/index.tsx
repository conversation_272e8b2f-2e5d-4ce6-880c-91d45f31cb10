import React, { useEffect, useId, useMemo, useRef, useState } from 'react'
import {
  ArrowLeft,
  Check,
  ChevronRight,
  CircleX,
  Edit2,
  Loader2,
  Plus,
  Save,
  Share2,
  <PERSON><PERSON>lert,
  <PERSON><PERSON><PERSON><PERSON>,
  Trash,
  Video
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button.tsx'
import { Card } from '@/components/ui/card.tsx'
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table.tsx'
import { Select, SelectContent, SelectItem, SelectValue } from '@/components/ui/select.tsx'
import { SelectTrigger } from '@radix-ui/react-select'
import { Textarea } from '@/components/ui/textarea.tsx'
import { Input } from '@/components/ui/input.tsx'
import Editor from 'react-simple-code-editor'
import './script.css'
import { Link } from 'react-router'
import { cn } from '@/components/lib/utils.ts'
import { uploadB<PERSON>erViaIPC } from '@/libs/request/upload.ts'
import { ScriptSceneData, useQueryScript } from '@/hooks/queries/useQueryScript.ts'
import { PageLoading } from '@/components/LoadingIndicator.tsx'
import { ResourceModule } from '@/libs/request/api/resource.ts'
import { debounce, isEqual } from 'lodash'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys.ts'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore.ts'
import { ComplianceAPI, RiskKeyword } from '@/libs/request/api/secure.ts'
import { toast } from 'react-toastify'
import { useVirtualTab } from '@/contexts'
import { UploadModule } from '@app/shared/types/ipc/file-uploader.ts'

const shotTypes = [
  { value: 'long', label: '远景' },
  { value: 'medium', label: '中景' },
  { value: 'close', label: '近景' },
  { value: 'closeup', label: '特写' },
  { value: 'full', label: '全景' },
]

function useAutoSave(callback: () => void | Promise<void>, deps: unknown[] = []) {
  const [isDirty, setIsDirty] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const callbackRef = useRef(callback)

  useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  const debounced = useMemo(
    () =>
      debounce(async () => {
        setIsSaving(true)
        try {
          await callbackRef.current()
          setLastSaved(new Date())
          setIsDirty(false)
        } catch (error) {
          console.error('Error saving:', error)
        } finally {
          setIsSaving(false)
        }
      }, 1000),
    [],
  )

  useEffect(() => {
    setIsDirty(true)
    debounced()
  }, [...deps, debounced])

  return { isDirty, isSaving, lastSaved, saveNow: debounced.flush }
}

function ScriptEditor({ value, onChange }: { value: string; onChange: (value: string) => void }) {
  const [keywords, setKeywords] = useState<RiskKeyword[]>([])
  const { isDirty } = useAutoSave(async () => {
    const keywords = await ComplianceAPI.checkRiskKeywords({ text: value })
    setKeywords(keywords)
  }, [value])

  return (
    <>
      <Editor
        className="h-full w-full editor"
        value={value}
        onValueChange={onChange}
        padding={10}
        highlight={text => {
          return text
            .split('\n')
            .map(
              (line, i) =>
                `<span class="editorLineNumber">${i + 1}.</span>${line || '<span class="editorPlaceholder">请输入或添加台词</span>'}`,
            )
            .join('\n')
        }}
      />

      {isDirty && value && (
        <div className="absolute left-2 bottom-2 bg-blue-50 dark:bg-blue-900 rounded p-1 pl-5 flex gap-1 items-center">
          <Loader2 className="absolute left-1 size-3.5 text-blue-400 dark:text-blue-200 animate-spin" />
          <span className="text-xs font-medium leading-none text-blue-600 dark:text-blue-400">风险词检测中...</span>
        </div>
      )}

      {!isDirty && value && !keywords.length && (
        <div className="absolute left-2 bottom-2 bg-green-50 dark:bg-green-900 rounded p-1 pl-5 flex gap-1 items-center">
          <ShieldCheck className="absolute left-[3px] size-4 fill-green-400 text-green-100 dark:fill-green-400 dark:text-green-800" />
          <span className="text-xs font-medium leading-none text-green-600 dark:text-green-500">未检测到风险词</span>
        </div>
      )}

      {!isDirty && value && !!keywords.length && (
        <div className="absolute left-2 bottom-2 bg-red-50 dark:bg-red-900 rounded p-1 pl-5 flex gap-1 items-center">
          <ShieldAlert className="absolute left-[3px] size-4 fill-red-400 text-red-100 dark:fill-red-300 dark:text-red-800" />
          <span className="text-xs font-medium leading-none text-red-600 dark:text-red-200">检测到 1 个风险词</span>
        </div>
      )}
    </>
  )
}

export default function ScriptEditPage() {
  const { pushNamedTab } = useVirtualTabsStore()

  const { params: { id: scriptId } } = useVirtualTab('Script')
  const queryClient = useQueryClient()
  const { data } = useQueryScript(scriptId!)

  const [scenes, setScenes] = useState<ScriptSceneData[] | undefined>(undefined)
  const [openSelects, setOpenSelects] = useState<Record<string, boolean>>({})
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [projectTitle, setProjectTitle] = useState('分镜脚本')

  const { isDirty, isSaving, lastSaved, saveNow } = useAutoSave(async () => {
    if (isEqual(scenes, data?.content)) return

    await ResourceModule.script.update({
      id: Number(scriptId!),
      content: JSON.stringify(scenes),
      fullContent: '[]',
    })
    await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SCRIPT_DETAIL, scriptId] })
  }, [scenes])

  useEffect(() => {
    if (!data?.scenes || scenes) return
    setScenes(data.scenes)
  }, [data, scenes])

  const updateScene = <K extends keyof ScriptSceneData>(id: ScriptSceneData['id'], field: K, value: ScriptSceneData[K]) => {
    if (!scenes) return
    setScenes(scenes.map(scene => (scene.id === id ? { ...scene, [field]: value } : scene)))
  }

  const newScene = (): ScriptSceneData => {
    return { id: crypto.randomUUID() }
  }

  const addNewScene = () => {
    if (!scenes) return
    setScenes([...scenes, newScene()])
  }

  const handleSelectOpenChange = (sceneId: string, open: boolean) => {
    setOpenSelects(prev => ({ ...prev, [sceneId]: open }))
  }

  const handleTitleEdit = () => {
    setIsEditingTitle(true)
  }

  const handleTitleBlur = async () => {
    setIsEditingTitle(false)
    await ResourceModule.script.rename({ id: Number(scriptId!), title: projectTitle })
  }

  const SmallUploadArea = ({ src, onUpdate }: { src?: string; onUpdate?: (url?: string) => unknown }) => {
    const id = useId()

    return (
      <div
        className={cn(
          'group border border-dashed rounded text-center',
          'transition-colors cursor-pointer size-24 flex flex-col justify-center items-center relative',
          !src && 'hover:border-primary text-muted-foreground',
        )}
      >
        {src && <img src={src} alt="参考画面" className="size-full object-cover rounded z-0" />}
        <label
          htmlFor={id}
          className={cn(
            'absolute inset-0 flex flex-col items-center justify-center hover:opacity-100 z-10 transition-all',
            src ? 'opacity-0 bg-background/60' : 'opacity-50',
          )}
        >
          <Plus className="w-4 h-4 mx-auto mb-1" />
          <div className="text-xs">上传参考画面</div>
          {src && (
            <Button
              size="icon"
              variant="ghost"
              className="size-6"
              onClick={e => {
                e.stopPropagation()
                onUpdate?.()
              }}
            >
              <Trash className="size-4" />
            </Button>
          )}
        </label>
        <input
          id={id}
          className="hidden"
          type="file"
          accept="image/png,image/jpeg"
          onChange={async e => {
            const [file] = e.target.files ?? []
            if (!file) return
            const buffer = await file.arrayBuffer()
            const uuid = crypto.randomUUID().replace(/-/g, '')
            const suffix = file.type.split('/').at(-1)
            const filename = suffix ? `${uuid}.${suffix}` : uuid
            const result = await uploadBufferViaIPC(buffer, filename, uuid, UploadModule.script)
            if (!result.success) toast.error('图片上传失败：' + result.error)
            onUpdate?.(result.url!)
          }}
        />
      </div>
    )
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 flex h-full flex-col gap-4 overflow-hidden">
      {/* Header */}
      <div className="flex items-center h-10 border-gray-200 gap-3 text-nowrap">
        {isEditingTitle ? (
          <>
            <Input
              value={projectTitle}
              onChange={e => setProjectTitle(e.target.value)}
              onBlur={handleTitleBlur}
              autoFocus
              className="text-xl font-semibold border-0 shadow-none p-0 h-auto bg-transparent focus-visible:ring-0 outline-none"
              style={{ boxShadow: 'none', outline: 'none' }}
            />
            <Button variant="ghost" size="sm" onClick={handleTitleEdit} className="p-1 h-auto hover:bg-gray-100">
              <Check className="w-4 h-4 text-gray-500" />
            </Button>
          </>
        ) : (
          <>
            <span className="text-xl font-semibold">{projectTitle}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleTitleEdit}
              className="p-1 h-auto hover:bg-muted-foreground/15"
            >
              <Edit2 className="w-4 h-4 text-muted-foreground" />
            </Button>
          </>
        )}
        {scenes && (
          <div className="text-sm text-muted-foreground flex items-center gap-1">
            {isSaving ? (
              <>
                <span>保存中</span>
                <Loader2 className="size-4 animate-spin" />
              </>
            ) : isDirty ? (
              <>
                <span>已编辑</span>
                <Button variant="ghost" size="icon" onClick={saveNow} className="size-6 h-auto hover:bg-gray-100">
                  <Save className="size-4" />
                </Button>
              </>
            ) : (
              `已保存 ${new Date(lastSaved!).toLocaleTimeString('zh-CN')}`
            )}
          </div>
        )}
        <Button
          variant="secondary"
          size="sm"
          className={cn('flex items-center gap-2', isEditingTitle && 'hidden')}
          asChild
        >
          <Link to="../.." relative="path">
            <ArrowLeft className="w-4 h-4" />
            返回上一级
          </Link>
        </Button>
        <Button variant="secondary" size="sm" className={cn('flex items-center gap-2', isEditingTitle || 'ml-auto')}>
          <Share2 className="w-4 h-4" />
          分享脚本
        </Button>
        <Button
          size="sm"
          className="flex items-center gap-2"
          onClick={() => {
            if (data) {
              pushNamedTab('Editor', {
                id: scriptId,
                projectId: data.projectId.toString()
              })
            }
          }}
        >
          <Video className="w-4 h-4" />
          前往混剪
        </Button>
      </div>

      <Card className="border border-border shadow-sm flex-1 min-h-0 *:h-full *:rounded-xl bg-transparent">
        <Table className="table-fixed border-b">
          <TableHeader className="sticky top-0 rounded-sm bg-muted z-10">
            <TableRow className="rounded-lg *:border-r *:border-dashed *:bg-main">
              <TableHead className="w-32 font-medium border-dashed">分镜编号</TableHead>
              <TableHead className="w-32 font-medium border-dashed">景别</TableHead>
              <TableHead className="font-medium border-dashed">
                <div className="flex items-center justify-between">
                  <span>话术</span>
                </div>
              </TableHead>
              <TableHead className="w-80 font-medium">参考画面要求/备注</TableHead>
            </TableRow>
          </TableHeader>
          {scenes?.length ? (
            <TableBody className="overflow-auto">
              {scenes.map((scene, index) => (
                <TableRow key={scene.id} className="h-60 *:border-r *:border-dashed">
                  <TableCell className="group relative">
                    <div>
                      <div className="text-base mb-2">分镜 {index + 1}</div>
                      <Input
                        placeholder="点击输入分镜名称"
                        value={scene.title}
                        onChange={e => updateScene(scene.id, 'title', e.target.value)}
                        className="rounded-none border-0 shadow-none p-0 h-auto text-sm focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent focus:bg-transparent hover:bg-transparent outline-none focus:outline-none"
                        style={{ boxShadow: 'none', outline: 'none' }}
                      />
                      <div className="flex absolute left-2 bottom-2 gap-2">
                        <Button
                          variant="secondary"
                          size="icon"
                          className="size-5 rounded bg-muted-foreground/60 hover:bg-muted-foreground/80 hidden group-hover:flex"
                          onClick={() => {
                            return setScenes(scenes.flatMap(s => (s.id === scene.id ? [s, newScene()] : [s])))
                          }}
                        >
                          <Plus className="size-4 text-white" />
                        </Button>
                        <Button
                          variant="secondary"
                          size="icon"
                          className="size-5 rounded bg-muted-foreground/60 hover:bg-muted-foreground/80 hidden group-hover:flex"
                          onClick={() => setScenes(scenes.filter(s => s.id !== scene.id))}
                        >
                          <Trash className="size-3.5 text-white" />
                        </Button>
                      </div>
                    </div>
                  </TableCell>

                  <TableCell className="text-center">
                    <div className="group inline-flex items-center gap-1 relative">
                      <Select
                        value={scene.shotType ?? ''}
                        onValueChange={value => updateScene(scene.id, 'shotType', value)}
                        onOpenChange={open => handleSelectOpenChange(scene.id, open)}
                      >
                        <SelectTrigger
                          className="border-0 shadow-none focus:ring-0 focus:ring-offset-0 bg-transparent
                        focus:bg-transparent hover:bg-transparent data-[state=open]:bg-transparent
                        outline-none focus:outline-none p-0 h-auto inline-flex items-center justify-between px-1"
                          style={{ boxShadow: 'none', outline: 'none' }}
                          asChild
                        >
                          <Button variant="secondary">
                            <SelectValue placeholder={<span className="text-muted-foreground">选择场景</span>} />
                            {!scene.shotType && (
                              <ChevronRight
                                className={`w-4 h-4 text-gray-400 transition-transform duration-200 flex-shrink-0 ${
                                  openSelects[scene.id] ? 'rotate-90' : 'rotate-0'
                                }`}
                              />
                            )}
                          </Button>
                        </SelectTrigger>
                        <SelectContent>
                          {shotTypes.map(type => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {scene.shotType && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 translate-x-1/1 hidden group-hover:inline-flex size-5 rounded-sm"
                          onClick={() => updateScene(scene.id, 'shotType', '')}
                        >
                          <CircleX className="size-4 fill-primary/40 text-background" />
                        </Button>
                      )}
                    </div>
                  </TableCell>

                  <TableCell className="h-60 overflow-hidden p-0 relative">
                    <ScriptEditor
                      value={scene.script ?? ''}
                      onChange={value => updateScene(scene.id, 'script', value)}
                    />
                  </TableCell>

                  <TableCell className="align-top h-60">
                    <div className="flex flex-col h-full">
                      <Textarea
                        placeholder="请输入画面要求，如：从包装盒取出护肤品"
                        value={scene.notes ?? ''}
                        onChange={e => updateScene(scene.id, 'notes', e.target.value)}
                        className="rounded-none resize-none border-0 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 p-0 bg-transparent focus:bg-transparent hover:bg-transparent outline-none focus:outline-none"
                        style={{ boxShadow: 'none', outline: 'none' }}
                      />
                      <div className="flex justify-start mt-auto">
                        <SmallUploadArea src={scene.refImg} onUpdate={url => updateScene(scene.id, 'refImg', url)} />
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <TableCaption>
              {scenes ? (
                <div className="flex flex-col items-center gap-2">
                  <div className="text-sm text-gray-500">暂无分镜</div>
                </div>
              ) : (
                <PageLoading />
              )}
            </TableCaption>
          )}
        </Table>
      </Card>

      <Button variant="secondary" size="lg" disabled={!scenes} onClick={addNewScene}>
        <Plus className="w-4 h-4 mr-2" />
        新增分镜
      </Button>
    </div>
  )
}
