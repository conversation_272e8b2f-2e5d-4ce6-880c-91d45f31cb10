import React from 'react'
import { Overlay } from '@app/rve-shared/types'
import { RotateCw } from 'lucide-react'
import { useDraggable } from '@dnd-kit/core'
import { LAYER_CONTROL_DRAG_ACTIONS } from './constants.ts'

/**
 * RotateHandle Component
 *
 * A React component that provides rotation functionality for overlay elements.
 * Renders a rotation handle that users can drag to rotate the parent overlay.
 *
 * @component
 *
 * @example
 * <RotateHandle
 *   overlay={overlayObject}
 *   setOverlay={(id, updater) => updateOverlay(id, updater)}
 * />
 */
export const RotateHandle: React.FC<{
  overlay: Overlay
  outlineRef: React.Ref<HTMLDivElement>
}> = ({ overlay, outlineRef }) => {
  const { setNodeRef, listeners, attributes } = useDraggable({
    id: `layer-rotate-${overlay.id}`,
    data: {
      action: LAYER_CONTROL_DRAG_ACTIONS.rotate,
      overlay,
      outlineRef
    }
  })

  return (
    <div
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      style={{
        position: 'absolute',
        width: '32px',
        height: '32px',
        cursor: 'pointer',
        top: '-68px',
        left: '50%',
        transform: 'translateX(-50%)',
        display: 'flex',
        alignItems: 'center',

        justifyContent: 'center',
      }}
    >
      <RotateCw size={102} strokeWidth={2.5} color="#3B8BF2" />
    </div>
  )
}
