import React, { FC, useCallback } from 'react'
import { Overlay, OverlayType } from '@app/rve-shared/types'
import { TextSetting } from '@rve/editor/components/overlays/text/text-setting.tsx'
import { SoundSetting } from '@rve/editor/components/overlays/sounds/sound-setting.tsx'
import { VideoSetting } from '@rve/editor/components/overlays/video/video-setting.tsx'
import { CaptionSetting } from '@rve/editor/components/overlays/captions/caption-setting.tsx'
import { ImageSetting } from '@rve/editor/components/overlays/images/image-setting.tsx'
import {
  OverlayEditingContext,
  OverlayEditingContextValues,
  useCachedOverlaysContext,
  useEditorContext
} from '@rve/editor/contexts'
import { merge } from 'lodash'
import { SingleOverlayUpdatePayload } from '@rve/editor/contexts/editor/useOverlays.tsx'

export const OverlayEditingPanel: React.FC<{ localOverlay: Overlay }> = ({ localOverlay }) => {
  const { updateOverlay } = useEditorContext()
  const { requestUpdate } = useCachedOverlaysContext()

  const handleUpdateOverlay = useCallback<OverlayEditingContextValues['requestUpdate']>(
    (updater, commit = false) => {
      if (!localOverlay) return

      const updatedOverlay = (typeof updater === 'function') && localOverlay
        ? updater(localOverlay as any)
        : merge({}, localOverlay, updater) as SingleOverlayUpdatePayload

      requestUpdate(localOverlay.id, updatedOverlay)

      if (commit) {
        updateOverlay(localOverlay.id, () => updatedOverlay)
      }
    },
    [updateOverlay, localOverlay]
  )

  const overlayTypeComponentMap: Partial<Record<OverlayType, FC>> = {
    [OverlayType.TEXT]: TextSetting,
    [OverlayType.SOUND]: SoundSetting,
    [OverlayType.VIDEO]: VideoSetting,
    [OverlayType.CAPTION]: CaptionSetting,
    [OverlayType.STICKER]: ImageSetting,
  }

  const Component = overlayTypeComponentMap[localOverlay.type]

  return (
    <div className="p-4 h-full overflow-y-auto">
      <OverlayEditingContext
        value={{
          localOverlay,
          requestUpdate: handleUpdateOverlay
        }}
      >
        {Component && <Component />}
      </OverlayEditingContext>
    </div>
  )
}
