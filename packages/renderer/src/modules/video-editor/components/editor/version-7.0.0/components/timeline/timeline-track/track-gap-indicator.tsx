import { X } from 'lucide-react'
import React, { useCallback } from 'react'
import { ENABLE_REMOVE_GAP_BUTTON, PIXELS_PER_FRAME } from '@rve/editor/constants'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'

/**
 * Props for the GapIndicator component
 */
interface GapIndicatorProps {
  /** The gap object containing start and end timestamps */
  gap: { start: number, end: number }
  /** The index of the row where this gap appears */
  rowIndex: number
}

/**
 * A component that displays a visual indicator for gaps in a timeline.
 * It shows a striped pattern on hover and includes a close button to remove the gap.
 * The width and position of the indicator are calculated as percentages of the total duration.
 *
 * @param props - The component props
 * @param props.gap - Object containing start and end times of the gap
 * @param props.rowIndex - Index of the timeline row
 */
export function TrackGapIndicator({
  gap,
  rowIndex,
}: GapIndicatorProps) {
  const { zoomScale } = useTimeline()
  const { tracks, bulkUpdateOverlays } = useEditorContext()

  const handleRemoveGap = useCallback(
    (rowIndex: number, gapStart: number, gapEnd: number) => {
      const overlaysToShift = tracks[rowIndex]
        .overlays
        .filter(overlay => overlay.from >= gapEnd)
        .sort((a, b) => a.from - b.from)

      if (overlaysToShift.length === 0) return

      const firstOverlayAfterGap = overlaysToShift[0]
      const gapSize = firstOverlayAfterGap.from - gapStart

      if (gapSize <= 0) return

      const updates = overlaysToShift.map(overlay => ({
        ...overlay,
        from: overlay.from - gapSize,
      }))

      bulkUpdateOverlays(updates)
    },
    [tracks, bulkUpdateOverlays],
  )

  const removeGapButton = (
    <div
      className="cursor-pointer bg-slate-900/60 dark:bg-black/70 rounded-full p-1 backdrop-blur-sm"
      onClick={e => {
        e.stopPropagation()
        handleRemoveGap(rowIndex, gap.start, gap.end)
      }}
    >
      <X className="size-3" />
    </div>
  )

  return (
    <div
      className="absolute top-0 bottom-0 w-full h-full group z-10"
      style={{
        left: gap.start * PIXELS_PER_FRAME * zoomScale,
        width: (gap.end - gap.start) * PIXELS_PER_FRAME * zoomScale,
      }}
    >
      <div
        className={`
          absolute top-0 bottom-0 left-0 right-0 w-full h-full
          opacity-0 group-hover:opacity-100 transition-all duration-200
        `}
        style={{
          background: `repeating-linear-gradient(
            -45deg,
            rgba(100, 116, 139, 0.02),
            rgba(100, 116, 139, 0.02) 8px,
            rgba(100, 116, 139, 0.05) 8px,
            rgba(100, 116, 139, 0.05) 16px
          )`,
          border: '1px dashed rgba(100, 116, 139, 0.2)',
        }}
      />
      <div
        className={`
          opacity-0 group-hover:opacity-100 transition-opacity duration-200
          absolute top-0 bottom-0 left-0 right-0 w-full h-full flex items-center justify-center
        `}
      >
        {ENABLE_REMOVE_GAP_BUTTON && removeGapButton}
      </div>
    </div>
  )
}
