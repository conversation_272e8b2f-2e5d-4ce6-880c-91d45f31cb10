import React, { memo, useCallback, useState, useMemo, useEffect } from 'react'
import { MaterialResource, ResourceSource } from '@/types/resources.ts'
import { UploadIcon, FolderUpIcon, ChevronDown } from 'lucide-react'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover.tsx'
import { Button } from '@/components/ui/button.tsx'
import { useQueryMaterialDirectoryList, useQueryMediaList } from '@/hooks/queries/useQueryMaterial.ts'
import { FolderUploader } from '@/components/ui/folder-uploader.tsx'
import { FileUploader, FileUploaderRenderProps } from '@/components/ui/file-uploader.tsx'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys.ts'
import { useEditorContext } from '@rve/editor/contexts'
import { SortMenu } from '@/pages/Projects/material/components/MaterialFilterBar.tsx'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.tsx'
import { SearchInput } from '@/components/ui/search-input.tsx'
import MediaTypeSelector from '@/pages/Projects/material/components/MediaTypeSelector.tsx'
import { InfiniteResourceList } from '@/components/InfiniteResourceList.tsx'
import { useItemActions } from '@/hooks/useItemActions'

const EditorMediaItem = memo(
  ({
    item,
    onItemClick,
    onItemAdd,
  }: {
    item: MaterialResource.Media
    onItemClick?: () => void
    onItemAdd: () => void
  }) => {
    console.log(item)
    console.log(onItemClick)
    console.log(onItemAdd)

    return <div className="relative">媒体资源</div>
  },
)

EditorMediaItem.displayName = 'EditorMediaItem'

/**
 * 素材库组件
 */
export function MaterialLibPanel() {
  const { projectId } = useEditorContext()
  const queryClient = useQueryClient()
  const [activeTab, setActiveTab] = useState(0)
  const { data: treeData, isSuccess: isTreeSuccess } = useQueryMaterialDirectoryList(
    {
      projectId: Number(projectId),
    },
    { enabled: !!projectId },
  )
  const defaultFolder = useMemo(
    () => (isTreeSuccess && treeData?.length ? treeData[0] : undefined),
    [isTreeSuccess, treeData],
  )

  const { createItem } = useItemActions()

  const [filters, setFilters] = useState<MaterialResource.MaterialMediaParams>({
    projectId: Number(projectId),
    folderUuid: '',
    sortField: MaterialResource.SortField.UPLOAD_TIME,
    sortOrder: MaterialResource.SortOrder.ASC,
    createAtRange: [],
    durationRange: [],
    useCountRange: undefined, // 合成次数
    quoteCountRange: undefined, // 引用次数
    keyword: undefined,
    resType: undefined,
  })
  const inputs = [
    { placeholder: '搜索关键词', field: 'keyword' },
    { placeholder: '合成次数', field: 'useCountRange', isNumber: true },
    { placeholder: '引用次数', field: 'quoteCountRange', isNumber: true },
  ]
  const mediaQueryResult = useQueryMediaList(filters, isTreeSuccess && filters.folderUuid !== '')

  const handleMaterialClick = useCallback(async (data: MaterialResource.Media) => {
    try {
      console.log('点击添加到轨道?', data)
    } catch (error) {
      console.error('失败:', error)
    }
  }, [])

  // 素材包装组件，处理异步加载状态
  const MediaItemWrapper = useCallback(
    ({ item, index }: { item: MaterialResource.Media; index: number }) => {
      return (
        <EditorMediaItem
          key={`sound-${item.fileId}-${index}`}
          item={item}
          onItemAdd={() => handleMaterialClick(item)}
        />
      )
    },
    [handleMaterialClick],
  )
  const renderMediaItem = useCallback(
    (item: MaterialResource.Media, index: number) => {
      return <MediaItemWrapper item={item} index={index} />
    },
    [MediaItemWrapper],
  )

  const renderMediaContent = useCallback(() => {
    return (
      <InfiniteResourceList
        queryResult={mediaQueryResult}
        renderItem={renderMediaItem}
        emptyText="暂无素材"
        loadingText="加载素材中..."
        itemsContainerClassName="grid grid-cols-4 gap-3 pt-3 pb-3"
      />
    )
  }, [mediaQueryResult, renderMediaItem])

  // const hasSounds = useMemo(() => {
  //   const firstPage = mediaQueryResult.data?.pages?.[0]
  //   return !!firstPage && firstPage.list.length > 0
  // }, [mediaQueryResult.data])

  const CustomUploader: React.FC<FileUploaderRenderProps> = ({ getRootProps, getInputProps, isLoading }) => {
    return (
      <div className="flex items-center justify-center gap-2">
        <UploadIcon className="w-3.5 h-3.5" />
        <div {...getRootProps()}>
          <input {...getInputProps()} />
          {isLoading ? '上传中...' : '上传文件'}
        </div>
      </div>
    )
  }
  useEffect(() => {
    if (defaultFolder) {
      setFilters(prev => ({ ...prev, folderUuid: defaultFolder.id }))
    }
  }, [defaultFolder])
  return (
    <div className="flex flex-col h-full w-full overflow-hidden px-2 py-1">
      <div className="flex justify-between gap-1 py-1">
        {/* 上传 */}

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" className="w-36 border-0 bg-primary/10 flex justify-between gap-1">
              <span>上传</span>
              <ChevronDown className="w-3.5 h-3.5" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-36 p-0">
            <div className="py-1">
              <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
                <FileUploader
                  folderUuid={filters.folderUuid}
                  renderCustomComponent={props => <CustomUploader {...props} />}
                />
              </button>
              <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
                <FolderUploader
                  resourceType="material"
                  folderUuid={filters.folderUuid}
                  children={
                    <div className="flex items-center justify-center">
                      <FolderUpIcon className="w-3.5 h-3.5 mr-2" />
                      上传文件夹
                    </div>
                  }
                  isShowUploadedFiles={false}
                  showFileList={false}
                  onProgress={(current, total) => {
                    console.log({ current, total })
                  }}
                  onUpload={async () => {
                    await queryClient.invalidateQueries({
                      queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST],
                    })
                  }}
                />
              </button>
            </div>
          </PopoverContent>
        </Popover>

        {/* 新建文件夹 */}
        <Button
          variant="ghost"
          size="sm"
          className="px-4 h-7 bg-primary/10 shadow-md"
          onClick={() =>
            createItem(ResourceSource.FOLDER, filters.folderUuid, {
              label: '文件夹名称',
              headerTitle: '文件夹',
            })
          }
        >
          新建文件夹
        </Button>
      </div>
      <div className="flex justify-between mt-2">
        {/* 下拉列表：树列表 */}
        <Select
          value={filters.folderUuid}
          onValueChange={value => setFilters(prev => ({ ...prev, folderUuid: value }))}
        >
          <SelectTrigger
            className="w-32 mb-2 text-sm border-0 bg-primary/10 h-7 w-full mr-2"
            style={{ fontSize: '12px' }}
          >
            <SelectValue placeholder="项目" />
          </SelectTrigger>
          <SelectContent>
            {treeData?.map(opt => (
              <SelectItem key={opt.id} value={opt.id}>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {/* 排序 */}
        <SortMenu
          sortField={filters.sortField}
          sortOrder={filters.sortOrder}
          size="sm"
          onChange={(field, order) =>
            setFilters(prev => ({
              ...prev,
              sortField: field,
              sortOrder: order,
            }))
          }
        />
      </div>
      {/* 筛选 */}
      <div className="flex gap-1">
        {inputs.map(({ placeholder, field, isNumber }) => (
          <SearchInput
            key={field}
            placeholder={placeholder}
            value={filters[field] ?? ''}
            onChange={e =>
              setFilters(prev => ({
                ...prev,
                [field]: isNumber ? Number(e.target.value) : e.target.value,
              }))
            }
            containerClassName="w-30%"
            size="xs"
          />
        ))}
      </div>
      {/* 分类筛选 */}
      <div>
        <MediaTypeSelector activeTab={activeTab} isEdit={true} setActiveTab={setActiveTab} setFilters={setFilters} />
      </div>
      <div>{renderMediaContent()}</div>
    </div>
  )
}

export default memo(MaterialLibPanel)
