import React, { FC, useMemo } from 'react'
import { useCachedOverlaysContext, useEditorContext } from '@rve/editor/contexts'
import { Overlay } from '@app/rve-shared/types'
import { useOperationPanelStore } from '@rve/editor/stores/useOperationPanelStore.ts'

import { TextToSpeechPanel } from './text-to-speech-panel.tsx'
import { OverlayEditingPanel } from './overlay-editing-panel.tsx'
import { GlobalSetting } from './global-setting.tsx'

export const EditorOperation: FC = () => {
  const { selectedOverlay } = useEditorContext()
  const { overlays } = useCachedOverlaysContext()

  const localOverlay = useMemo<Overlay | null>(
    () => overlays.find(o => o.id === selectedOverlay?.id) || null,
    [overlays, selectedOverlay]
  )

  const { activePanel } = useOperationPanelStore()

  if (activePanel === 'text-to-speech') {
    return <TextToSpeechPanel />
  }

  if (localOverlay) {
    return <OverlayEditingPanel localOverlay={localOverlay} />
  }

  return (
    <div className="p-4 h-full overflow-y-auto">
      <GlobalSetting />
    </div>
  )
}
