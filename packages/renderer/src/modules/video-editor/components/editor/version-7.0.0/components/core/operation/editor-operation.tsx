import React, { FC, useMemo } from 'react'
import { useCachedOverlaysContext, useEditorContext } from '@rve/editor/contexts'
import { Overlay } from '@app/rve-shared/types'
import { GlobalSetting } from '../../overlays/common/global-setting.tsx'
import { OverlayEditingPanel } from '@rve/editor/components/core/operation/overlay-editing-panel.tsx'

export const EditorOperation: FC = () => {
  const { selectedOverlay } = useEditorContext()
  const { overlays } = useCachedOverlaysContext()

  const localOverlay = useMemo<Overlay | null>(
    () => overlays.find(o => o.id === selectedOverlay?.id) || null,
    [overlays, selectedOverlay]
  )

  if (!localOverlay) {
    return (
      <div className="p-4 h-full overflow-y-auto">
        <GlobalSetting />
      </div>
    )
  }

  return <OverlayEditingPanel localOverlay={localOverlay} />
}
