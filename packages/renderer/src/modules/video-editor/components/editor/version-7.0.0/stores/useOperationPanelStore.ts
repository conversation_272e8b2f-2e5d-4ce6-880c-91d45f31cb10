import { create } from 'zustand'
import { ScriptSceneData } from '@/hooks/queries/useQueryScript.ts'
import { TextOverlay } from '@app/rve-shared/types'

export type OperationPanels =
  | 'text-to-speech'
  | 'global-config'

type OperationPanelStoreStates = {
  activePanel: OperationPanels
  textToSpeechDatasource: ScriptSceneData[] | TextOverlay

  openTextToSpeechPanel(datasource: ScriptSceneData[] | TextOverlay): void
}

export const useOperationPanelStore = create<OperationPanelStoreStates>(
  (set, _get) => ({
    activePanel: 'global-config',
    textToSpeechDatasource: [],

    openTextToSpeechPanel(datasource: ScriptSceneData[] | TextOverlay) {
      set({ activePanel: 'text-to-speech', textToSpeechDatasource: datasource })
    }
  })
)
