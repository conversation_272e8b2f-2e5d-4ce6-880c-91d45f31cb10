import React, { useCallback, useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { SearchInput } from '@/components/ui/search-input'
import { cn } from '@/components/lib/utils'
import { Zap } from 'lucide-react'
import { FormSlider } from '@rve/editor/components/overlays/common/form-components.tsx'
import { ScriptSceneData } from '@/hooks/queries/useQueryScript.ts'
import { useEditorContext } from '@rve/editor/contexts'
import {
  TextToSpeechProgressOverlay,
  TTSProgressState,
  TTSTaskStatus
} from '@rve/editor/components/TextToSpeechProgressOverlay.tsx'
import { OverlayType, SoundOverlay, StoryboardOverlay, TextOverlay, Track, TrackType } from '@app/rve-shared/types'
import { DEFAULT_OVERLAY, DEFAULT_TEXT_FONT_SRC, DEFAULT_TEXT_OVERLAY_STYLES, FPS } from '@rve/editor/constants'
import { generateNewOverlayId } from '@rve/editor/utils/track-helper.ts'
import { toast } from 'react-toastify'
import { extractSentencesFromScriptScene } from '@/libs/tools/script.ts'
import { textToSpeech } from '@rve/editor/utils/ai-helper.ts'
import { TTSErrorReportDialog } from '@rve/editor/components/TTSErrorReportDialog.tsx'
import { useOperationPanelStore } from '@rve/editor/stores/useOperationPanelStore.ts'
import { useOverlayHelper } from '@rve/editor/hooks/helpers/useOverlayHelper.ts'

type AudioInfo = {
  durationInFrames: number;
  localUrl: string;
  originalUrl: string;
}

type AudioInfoMap = Map<string, AudioInfo>

type IndexedSentence = {
  text: string;
  sceneIndex: number;
  sentenceIndex: number
}

// 语言选项
const LANGUAGE_OPTIONS = [
  { value: 'all', label: '全部' },
  { value: 'favorite', label: '收藏' },
  { value: 'chinese', label: '中文' },
  { value: 'dialect', label: '方言' },
  { value: 'english', label: '英文' },
  { value: 'indonesian', label: '印尼' },
  { value: 'japanese', label: '日本' },
  { value: 'korean', label: '韩国' },
]

// 音色数据
const VOICE_OPTIONS = [
  { id: 1, name: '倪音娱乐男声', category: 'chinese', isFavorite: false },
  { id: 2, name: '湖北话网红北女孩', category: 'dialect', isFavorite: true },
  { id: 3, name: '好物男声', category: 'chinese', isFavorite: false },
  { id: 4, name: '和性女神', category: 'chinese', isFavorite: false },
  { id: 5, name: '韩性全能', category: 'korean', isFavorite: false },
  { id: 6, name: '闽南话女孩', category: 'dialect', isFavorite: false },
  { id: 7, name: '问南奇侠男声', category: 'chinese', isFavorite: false },
  { id: 8, name: 'English Female', category: 'english', isFavorite: false },
  { id: 9, name: 'Japanese Girl', category: 'japanese', isFavorite: false },
  { id: 10, name: '温柔女声', category: 'chinese', isFavorite: true },
  { id: 11, name: '磁性男声', category: 'chinese', isFavorite: false },
  { id: 12, name: '活泼少女', category: 'chinese', isFavorite: false },
  { id: 13, name: '成熟男性', category: 'chinese', isFavorite: false },
  { id: 14, name: '甜美女孩', category: 'chinese', isFavorite: true },
  { id: 15, name: '东北话大哥', category: 'dialect', isFavorite: false },
  { id: 16, name: '四川话妹子', category: 'dialect', isFavorite: false },
  { id: 17, name: 'American Male', category: 'english', isFavorite: false },
  { id: 18, name: 'British Female', category: 'english', isFavorite: false },
]

const useGenerateTextAndAudio = () => {
  const { textToSpeechDatasource } = useOperationPanelStore()

  const { generateSpeechForTextOverlay } = useOverlayHelper()
  const { tracks, updateTracks, getAspectRatioDimensions } = useEditorContext()

  // 文本转语音状态
  const [ttsProgressState, setTtsProgressState] = useState<TTSProgressState>({
    visible: false,
    completed: 0,
    total: 0,
    tasks: []
  })

  // 错误报告弹窗状态
  const [errorReportOpen, setErrorReportOpen] = useState(false)
  const [failedTasks, _setFailedTasks] = useState<TTSTaskStatus[]>([])

  /**
   * 基于带音频信息的句子列表生成轨道
   */
  const generateTracksFromAudioInfo = useCallback((
    scenes: ScriptSceneData[],
    sentences: Array<IndexedSentence & { audioInfo?: AudioInfo }>,
    width: number,
    height: number
  ): Track[] => {
    const MINIMUM_DURATION = FPS
    let nextOverlayId = generateNewOverlayId(tracks)

    // 按场景组织句子和音频信息
    const sceneData = new Map<number, {
      sentences: Array<{ text: string, sentenceIndex: number, audioInfo?: AudioInfo }>
      totalDuration: number
    }>(
      scenes.map((_, index) => {
        return [index, { sentences: [], totalDuration: MINIMUM_DURATION }]
      })
    )

    // 填充句子和音频信息
    sentences.forEach(sentence => {
      const sceneInfo = sceneData.get(sentence.sceneIndex)
      if (!sceneInfo) return
      const { audioInfo } = sentence

      const sentenceData = {
        text: sentence.text,
        sentenceIndex: sentence.sentenceIndex,
        audioInfo
      }

      sceneInfo.sentences.push(sentenceData)

      // 计算场景总时长（取最长的句子时长）
      if (audioInfo) {
        sceneInfo.totalDuration = Math.max(sceneInfo.totalDuration, audioInfo.durationInFrames)
      }
    })

    // 生成分镜轨道
    const storyboardTrack: Track = {
      type: TrackType.STORYBOARD,
      overlays: []
    }

    let currentFrame = 0
    scenes.forEach((_, sceneIndex) => {
      const sceneInfo = sceneData.get(sceneIndex)
      if (!sceneInfo) return

      const storyboardOverlay: StoryboardOverlay = {
        ...DEFAULT_OVERLAY,
        id: nextOverlayId++,
        type: OverlayType.STORYBOARD,
        from: currentFrame,
        durationInFrames: sceneInfo.totalDuration,
      }

      storyboardTrack.overlays.push(storyboardOverlay)
      currentFrame += sceneInfo.totalDuration
    })

    // 计算需要的口播轨道数量
    const maxSentenceCount = Math.max(
      ...Array
        .from(sceneData.values())
        .map(info => info.sentences.length)
    )

    // 生成口播轨道
    const narrationTracks: Track[] = []

    for (let trackIndex = 0; trackIndex < maxSentenceCount; trackIndex++) {
      const narrationTrack: Track = {
        type: TrackType.NARRATION,
        overlays: []
      }

      let sceneStartFrame = 0

      scenes.forEach((_, sceneIndex) => {
        const sceneInfo = sceneData.get(sceneIndex)
        if (!sceneInfo) return

        const sentence = sceneInfo.sentences[trackIndex]
        if (!sentence || !sentence.text.trim()) {
          // 更新下一个分镜的起始帧位置
          sceneStartFrame += sceneInfo.totalDuration
          return
        }

        // 创建TextOverlay
        const textDuration = sentence.audioInfo?.durationInFrames || Math.max(FPS, sentence.text.length / 10 * FPS)

        const textOverlay: TextOverlay = {
          ...DEFAULT_OVERLAY,
          id: nextOverlayId++,
          type: OverlayType.TEXT,
          content: sentence.text.trim(),
          src: DEFAULT_TEXT_FONT_SRC,
          from: sceneStartFrame,
          durationInFrames: textDuration,
          width: width * 0.8,
          height: 100,
          left: width * 0.1,
          top: height * 0.8,
          storyboardIndex: sceneIndex,
          styles: {
            ...DEFAULT_TEXT_OVERLAY_STYLES,
            textAlign: 'center',
          }
        }

        narrationTrack.overlays.push(textOverlay)

        // 如果有音频且TTS成功（有原始URL），创建对应的SoundOverlay
        if (sentence.audioInfo && sentence.audioInfo.originalUrl) {
          const soundOverlay: SoundOverlay = {
            id: nextOverlayId++,
            type: OverlayType.SOUND,
            content: sentence.audioInfo.originalUrl, // 使用原始远程URL
            src: sentence.audioInfo.originalUrl,     // 使用原始远程URL
            localSrc: sentence.audioInfo.localUrl,   // 使用本地缓存URL
            durationInFrames: sentence.audioInfo.durationInFrames,
            from: sceneStartFrame,
            height: 100,
            width: 200,
            left: 0,
            top: 0,
            isDragging: false,
            rotation: 0,
            storyboardIndex: sceneIndex,
            styles: {
              volume: 1,
            },
          }

          narrationTrack.overlays.push(soundOverlay)
        }

        // 更新下一个分镜的起始帧位置
        sceneStartFrame += sceneInfo.totalDuration
      })

      narrationTracks.push(narrationTrack)
    }

    return [storyboardTrack, ...narrationTracks]
  }, [tracks])

  /**
   * 直接使用已处理的音频信息生成轨道
   */
  const generateTracksWithAudioInfo = useCallback(async (
    scenes: ScriptSceneData[],
    allSentences: IndexedSentence[],
    audioInfoMap: AudioInfoMap
  ) => {
    try {
      const { width, height } = getAspectRatioDimensions()

      const sentences: Array<IndexedSentence & { audioInfo?: AudioInfo }> = allSentences
        .map(sentence => ({
          ...sentence,
          audioInfo: audioInfoMap.get(sentence.text)
        }))

      const newTracks = generateTracksFromAudioInfo(scenes, sentences, width, height)

      // 更新轨道
      updateTracks(prevTracks => {
        const filteredTracks = prevTracks.filter(track =>
          track.type !== TrackType.STORYBOARD &&
          track.type !== TrackType.NARRATION
        )

        return [...newTracks, ...filteredTracks]
      })

      // 隐藏进度遮罩
      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })

      toast.success(`成功生成 ${audioInfoMap.size} 个语音轨道`)
    } catch (error) {
      console.error('生成音频轨道失败:', error)
      toast.error('生成音频轨道失败，请重试')

      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })
    }
  }, [updateTracks, getAspectRatioDimensions, generateTracksFromAudioInfo])

  const generateTextAndAudio = async (scenes: ScriptSceneData[]) => {
    if (!scenes.length) {
      toast.warning('没有可朗读的脚本内容')
      return
    }

    // 提取所有台词
    const allSentences: IndexedSentence[] = []
    scenes.forEach((scene, sceneIndex) => {
      const sentences = extractSentencesFromScriptScene(scene)
      sentences.forEach((sentence, sentenceIndex) => {
        if (sentence.trim()) {
          allSentences.push({ text: sentence.trim(), sceneIndex, sentenceIndex })
        }
      })
    })

    if (!allSentences.length) {
      toast.warning('没有找到有效的台词内容')
      return
    }

    // 初始化进度状态 - 简化为基于句子数量的进度计算
    setTtsProgressState({
      visible: true,
      completed: 0,
      total: allSentences.length,
      tasks: []
    })

    const audioInfoMap: AudioInfoMap = new Map()
    let completedSentencesCount = 0
    await Promise.all(
      allSentences.map(async sentence => {
        const [result] = await textToSpeech(sentence.text)
        completedSentencesCount++
        setTtsProgressState(prev => ({
          ...prev,
          completed: completedSentencesCount
        }))

        if (result) {
          const { audioUrl, localAudioUrl, durationInFrames } = result
          audioInfoMap.set(sentence.text, {
            originalUrl: audioUrl,
            localUrl: localAudioUrl,
            durationInFrames
          })
        }
      })
    )

    if (completedSentencesCount === 0) {
      toast.error('朗读功能执行失败，请重试')

      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })
      return
    }

    return generateTracksWithAudioInfo(scenes, allSentences, audioInfoMap)
  }

  const startGenerate = useCallback(async () => {
    if (Array.isArray(textToSpeechDatasource) && textToSpeechDatasource.length) {
      return generateTextAndAudio(textToSpeechDatasource)
    }

    if ('type' in textToSpeechDatasource && textToSpeechDatasource.type === OverlayType.TEXT) {
      return generateSpeechForTextOverlay(textToSpeechDatasource)
    }
  }, [textToSpeechDatasource])

  const ErrorReportDialog = useCallback(() => (
    <TTSErrorReportDialog
      open={errorReportOpen}
      onOpenChange={setErrorReportOpen}
      failedTasks={failedTasks}
      successCount={ttsProgressState.total - failedTasks.length}
      totalCount={ttsProgressState.total}
    />
  ), [errorReportOpen, failedTasks, ttsProgressState])

  const ProgressOverlay = useCallback(() => (
    <TextToSpeechProgressOverlay progressState={ttsProgressState} />
  ), [ttsProgressState])

  return {
    startGenerate,
    ErrorReportDialog,
    ProgressOverlay
  }
}

export const TextToSpeechPanel: React.FC = () => {
  const [speed, setSpeed] = useState(1)
  const [volume, setVolume] = useState(100)
  const [searchKeyword, setSearchKeyword] = useState('')
  const [syncGeneration, setSyncGeneration] = useState(true)
  const [selectedLanguage, setSelectedLanguage] = useState('all')
  const [autoDetectLanguage, setAutoDetectLanguage] = useState(true)
  const [selectedVoice, setSelectedVoice] = useState<number | null>(null)

  // 过滤音色选项
  const filteredVoices = VOICE_OPTIONS.filter(voice => {
    const matchesLanguage = selectedLanguage === 'all' ||
      (selectedLanguage === 'favorite' && voice.isFavorite) ||
      voice.category === selectedLanguage

    const matchesSearch = voice.name.toLowerCase().includes(searchKeyword.toLowerCase())

    return matchesLanguage && matchesSearch
  })

  const { startGenerate, ErrorReportDialog, ProgressOverlay } = useGenerateTextAndAudio()

  return (
    <div className="h-full flex flex-col bg-background">
      {/* 标题 */}
      <div className="p-4 border-b border-border">
        <h2 className="text-lg font-medium text-foreground">音频时长</h2>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* 音频生成选项 */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="sync-generation"
              checked={syncGeneration}
              onCheckedChange={v => setSyncGeneration(!!v)}
            />
            <Label htmlFor="sync-generation" className="text-sm">同步生成音频</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="parallel-generation"
              checked={!syncGeneration}
              onCheckedChange={checked => setSyncGeneration(!checked)}
            />
            <Label htmlFor="parallel-generation" className="text-sm">并行生成音频</Label>
          </div>
        </div>

        {/* 语音来源 */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-foreground">语音来源</h3>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="auto-detect"
              checked={autoDetectLanguage}
              onCheckedChange={v => setAutoDetectLanguage(!!v)}
            />
            <Label htmlFor="auto-detect" className="text-sm">自动检测语言</Label>
          </div>
          <Button variant="outline" size="sm" className="w-full justify-start">
            完整音色
          </Button>
        </div>

        {/* 通用设置 */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-foreground">通用设置</h3>

          {/* 语言选择按钮组 */}
          <div className="flex flex-wrap gap-2">
            {LANGUAGE_OPTIONS.map(option => (
              <Button
                key={option.value}
                variant={selectedLanguage === option.value ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedLanguage(option.value)}
                className={cn(
                  'text-xs',
                  selectedLanguage === option.value && 'bg-primary text-primary-foreground'
                )}
              >
                {option.label}
              </Button>
            ))}
          </div>

          {/* 搜索框 */}
          <SearchInput
            placeholder="搜索关键词"
            value={searchKeyword}
            onChange={e => setSearchKeyword(e.target.value)}
            size="default"
          />
        </div>

        {/* 音色选择网格 */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-foreground">音色选择</h3>
          <div className="grid grid-cols-3 gap-2 max-h-48 overflow-y-auto">
            {filteredVoices.map(voice => (
              <Button
                key={voice.id}
                variant={selectedVoice === voice.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedVoice(voice.id)}
                className={cn(
                  'h-auto p-2 text-xs text-center whitespace-normal leading-tight min-h-[2.5rem]',
                  selectedVoice === voice.id && 'bg-primary text-primary-foreground'
                )}
              >
                {voice.name}
              </Button>
            ))}
          </div>
          {filteredVoices.length === 0 && (
            <div className="text-center text-sm text-muted-foreground py-4">
              未找到匹配的音色
            </div>
          )}
        </div>

        {/* 调节参数 */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-foreground">调节</h3>

          {/* 语速控制 */}
          <FormSlider
            label="语速"
            value={speed}
            onChange={val => setSpeed(val)}
            min={0.5}
            max={2}
            step={0.1}
            showInput={true}
          />

          {/* 音量控制 */}
          <FormSlider
            label="音量"
            value={volume}
            onChange={val => setVolume(val)}
            min={0}
            max={100}
            step={1}
            showInput={true}
          />
        </div>
      </div>

      {/* 底部操作按钮 */}
      <div className="p-4 border-t border-border">
        <Button className="w-full" size="lg" onClick={startGenerate}>
          <Zap className="w-4 h-4 mr-2" />
          生成语音
        </Button>
      </div>

      <ErrorReportDialog />
      <ProgressOverlay />
    </div>
  )
}
