import { useHotkeys } from 'react-hotkeys-hook'
import { ZOOM_CONSTRAINTS } from '../constants'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'
import React from 'react'

/**
 * A custom hook that sets up keyboard shortcuts for timeline controls
 *
 * Keyboard shortcuts:
 * - Space: Play/Pause
 * - Cmd/Ctrl + Z: Undo
 * - Cmd/Ctrl + Shift + Z or Cmd/Ctrl + Y: Redo
 * - Alt + Plus/=: Zoom in
 * - Alt + Minus/-: Zoom out
 */
export const useTimelineShortcuts = (ref: React.RefObject<HTMLElement | null>) => {
  const {
    history: { canUndo, canRedo, undo, redo },
    videoPlayer: { togglePlayPause }
  } = useEditorContext()

  const { zoomScale, setZoomScale } = useTimeline()

  useHotkeys(
    'space',
    () => {
      togglePlayPause()
    },
    { preventDefault: true, enableOnFormTags: true },
  )(ref.current)

  useHotkeys(
    'meta+z, ctrl+z',
    () => {
      if (canUndo) undo()
    },
    { preventDefault: true }
  )(ref.current)

  useHotkeys(
    'meta+shift+z, ctrl+shift+z, meta+y, ctrl+y',
    () => {
      if (canRedo) redo()
    },
    { preventDefault: true }
  )(ref.current)

  useHotkeys(
    'alt+=, alt+plus',
    () => {
      setZoomScale(zoomScale + ZOOM_CONSTRAINTS.step)
    },
    { preventDefault: true }
  )(ref.current)

  useHotkeys(
    'alt+-, alt+minus',
    () => {
      setZoomScale(zoomScale - ZOOM_CONSTRAINTS.step)
    },
    {
      keydown: true,
      preventDefault: true,
    },
  )(ref.current)
}
