import React from 'react'
import { FolderOpen } from 'lucide-react'
import { OverlayType } from '@app/rve-shared/types'
import { registerMaterialPlugin } from '../registry'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'

const Panel = React.lazy(() =>
  import('@rve/editor/components/plugin-panels/material-lib.panel')
)

export default registerMaterialPlugin({
  id: ResourceType.MATERIAL,
  title: '素材库',
  icon: FolderOpen,
  component: Panel,
  overlayType: OverlayType.material,
  order: 1,
})

