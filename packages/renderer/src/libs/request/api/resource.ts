import request, { fetchPagination, fetchPaginationGet, requestCurrying } from '../request'
import { PaginationParams } from '@app/shared/types/database.types.ts'
import {
  CommonCategory,
  FontResource,
  FontStyleResource,
  MaterialResource,
  PasterResource,
  SoundResource,
  TimbreResource,
  UploadLocal
} from '@/types/resources'
import { Project, Script, Work } from '@/types/project'
import { PickRequired } from '@/types/utils'
import { OssModule } from '@/libs/request/api/oss.ts'

export const ResourceModule = {
  collect(resourcesId: string) {
    return request.post<boolean>('/app-api/creative/library/collect', { resourcesId })
  },
  cancelCollect(resourcesId: string) {
    return request.post<boolean>('/app-api/creative/library/cancel-collect', { resourcesId })
  },
  cover: (objectId: string) => OssModule.getObjectHref(objectId),

  /**
   * 贴纸
   */
  paster: {
    list: (params: PaginationParams & { categoryIds?: string[] }) => {
      return fetchPagination<PasterResource.Paster>('/app-api/creative/library/paster/search', params)
    },
    category: () => {
      return request.get<CommonCategory[]>('/app-api/creative/library/paster/category')
    },
    collected: (params: PaginationParams) => {
      return fetchPagination<PasterResource.Paster>('/app-api/creative/library/paster/collect-page', params)
    },
    //本地资源
    localList: requestCurrying.post<PaginationParams & { folderUuid: string }>('/app-api/creative/team/library/paster/page'),
    localCreate: requestCurrying.post<UploadLocal>('/app-api/creative/team/library/paster/create'),
    localRename: requestCurrying.post<{ fileId: string, fileName: string }>('/app-api/creative/team/library/paster/rename'),
    localDelete: requestCurrying.post<{ fileIds: string[] }>('/app-api/creative/team/library/paster/delete'),
    localMove: requestCurrying.post<{ fileIds: string[], folderUuid: string }>('/app-api/creative/team/library/paster/move'),
    //本地文件夹
    dirList: () => {
      return request.get('/app-api/creative/team/directory/paster/list')
    },
    dirCreate: requestCurrying.post<Pick<MaterialResource.Directory, 'parentId' | 'folderName'>>('/app-api/creative/team/directory/paster/create'),
    dirRename: requestCurrying.post<Pick<MaterialResource.Directory, 'folderId' | 'folderName'>>('/app-api/creative/team/directory/paster/rename'),
    dirDelete: requestCurrying.post<{ folderIds: string[] }>('/app-api/creative/team/directory/paster/delete'),
    dirMove: requestCurrying.post<{ folderIds: string[], parentId: string }>('/app-api/creative/team/directory/paster/move'),
  },

  /**
   * 音乐
   */
  music: {
    list: (params: PaginationParams) => {
      return fetchPagination<SoundResource.Sound>('/app-api/creative/library/music/search', params)
    },
    category: () => {
      return request.get<CommonCategory[]>('/app-api/creative/library/music/category')
    },
    collected: (params: PaginationParams) => {
      return fetchPagination<SoundResource.Sound>('/app-api/creative/library/music/collect-page', params)
    },
    rank: (params: PaginationParams) => {
      return fetchPagination<SoundResource.Sound>('/app-api/creative/library/music_rank/search', params)
    },
    //本地资源
    localList: requestCurrying.post<PaginationParams & { folderUuid: string }>('/app-api/creative/team/library/music/page'),
    localCreate: requestCurrying.post<UploadLocal>('/app-api/creative/team/library/music/create'),
    localRename: requestCurrying.post<{ fileId: string, fileName: string }>('/app-api/creative/team/library/music/rename'),
    localDelete: requestCurrying.post<{ fileIds: string[] }>('/app-api/creative/team/library/music/delete'),
    localMove: requestCurrying.post<{ fileIds: string[], folderUuid: string }>('/app-api/creative/team/library/music/move'),
    //本地文件夹
    dirList: () => {
      return request.get('/app-api/creative/team/directory/paster/list')
    },
    dirCreate: requestCurrying.post<Pick<MaterialResource.Directory, 'parentId' | 'folderName'>>('/app-api/creative/team/directory/music/create'),
    dirRename: requestCurrying.post<Pick<MaterialResource.Directory, 'folderId' | 'folderName'>>('/app-api/creative/team/directory/music/rename'),
    dirDelete: requestCurrying.post<{ folderIds: string[] }>('/app-api/creative/team/directory/music/delete'),
    dirMove: requestCurrying.post<{ folderIds: string[], parentId: string }>('/app-api/creative/team/directory/music/move'),
  },

  /**
   * 音效
   */
  voice: {
    list: (params: PaginationParams & { categoryIds?: string[] }) => {
      return fetchPagination<SoundResource.Sound>('/app-api/creative/library/voice/search', params)
    },
    category: () => {
      return request.get<CommonCategory[]>('/app-api/creative/library/voice/category')
    },
    collected: (params: PaginationParams) => {
      return fetchPagination<SoundResource.Sound>('/app-api/creative/library/voice/collect-page', params)
    },
    //本地资源
    localList: requestCurrying.post<PaginationParams & { folderUuid: string }>('/app-api/creative/team/library/voice/page'),
    localCreate: requestCurrying.post<UploadLocal>('/app-api/creative/team/library/voice/create'),
    localRename: requestCurrying.post<{ fileId: string, fileName: string }>('/app-api/creative/team/library/voice/rename'),
    localDelete: requestCurrying.post<{ fileIds: string[] }>('/app-api/creative/team/library/voice/delete'),
    localMove: requestCurrying.post<{ fileIds: string[], folderUuid: string }>('/app-api/creative/team/library/voice/move'),
    //本地文件夹
    dirList: () => {
      return request.get('/app-api/creative/team/directory/paster/list')
    },
    dirCreate: requestCurrying.post<Pick<MaterialResource.Directory, 'parentId' | 'folderName'>>('/app-api/creative/team/directory/voice/create'),
    dirRename: requestCurrying.post<Pick<MaterialResource.Directory, 'folderId' | 'folderName'>>('/app-api/creative/team/directory/voice/rename'),
    dirDelete: requestCurrying.post<{ folderIds: string[] }>('/app-api/creative/team/directory/voice/delete'),
    dirMove: requestCurrying.post<{ folderIds: string[], parentId: string }>('/app-api/creative/team/directory/voice/move'),
  },

  /**
   * 字体
   */
  font: {
    list: (params: PaginationParams) => {
      return fetchPagination<FontResource.Font>('/app-api/creative/library/font/search', params)
    },
    category: () => {
      return request.get<CommonCategory[]>('/app-api/creative/library/font/category')
    },
    collected: (params: PaginationParams) => {
      return fetchPagination<FontResource.Font>('/app-api/creative/library/font/collect-page', params)
    },
    bubbles: (params: PaginationParams) => {
      return fetchPagination<FontResource.BubbleLetters>('/app-api/creative/library/bubble_letters/search', params)
    }
  },

  /**
   * 花体字
   */
  fontStyle: {
    list: (params: PaginationParams) => {
      return fetchPagination<FontStyleResource.FontStyle>('/app-api/creative/library/font_style/search', params)
    },
  },

  /**
   * 项目
   */
  project: {
    list: fetchPaginationGet<Project>('/app-api/creative/project/page'),
    create: requestCurrying.post<Pick<Project, 'projectName'>, number>('/app-api/creative/project/create'),
    update: requestCurrying.post<Pick<Project, 'id' | 'projectName'>, boolean>('/app-api/creative/project/update'),
    delete: requestCurrying.delete<{ id: number }, void>('/app-api/creative/project/delete')
  },

  /**
   * 脚本
   */
  script: {
    list: fetchPaginationGet<Script>('/app-api/creative/script/page'),
    listRemoved: fetchPaginationGet<Script>('/app-api/creative/script/recycle-page'),
    get: requestCurrying.get<{ id: string }, Script>('/app-api/creative/script/get'),
    create: requestCurrying.post<Pick<Script, 'title' | 'projectId' | 'content' | 'fullContent'>, number>('/app-api/creative/script/create'),
    update: requestCurrying.post<PickRequired<Script, 'id'>, boolean>('/app-api/creative/script/update'),
    rename: requestCurrying.post<PickRequired<Script, 'id' | 'title'>, boolean>('/app-api/creative/script/rename'),
    duplicate: requestCurrying.post<{
      id: number,
      title: string,
      projectId: number
    }, number>('/app-api/creative/script/copy'),
    remove: requestCurrying.post<{ ids: number[] }, boolean>('/app-api/creative/script/recycle'),
    recover: requestCurrying.post<{ ids: number[] }, boolean>('/app-api/creative/script/back'),
  },

  /**
   * 作品
   */
  work: {
    list: fetchPaginationGet<Work>('/app-api/creative/video/remix/page'),
  },

  /**
   * 系统字典
   */
  systemDict: {
    getType: requestCurrying.get<{ type: string }, {
      label: string,
      value: string
    }[]>('/app-api/system/dict-data/type'),
  },

  /**
   * 素材文件夹
   */
  directory: {
    list: (params: MaterialResource.MaterialDirectoryParams) =>
      request.get('/app-api/creative/storage/directory/list', params),
    create: requestCurrying.post<Pick<MaterialResource.Directory, 'parentId' | 'folderName'>>('/app-api/creative/storage/directory/create'),
    rename: requestCurrying.post<Pick<MaterialResource.Directory, 'folderId' | 'folderName'>>('/app-api/creative/storage/directory/rename'),
    delete: requestCurrying.post<{ folderIds: string[] }>('/app-api/creative/storage/directory/delete'),
    move: requestCurrying.post<{ folderIds: string[], parentId: string }>('/app-api/creative/storage/directory/move'),
    recycle: requestCurrying.post<{ folderIds: string[] }>('/app-api/creative/storage/directory/recycle'),
    recycleList: (params: MaterialResource.MaterialMediaParams) => {
      return fetchPaginationGet<MaterialResource.Directory>('/app-api/creative/storage/directory/recycle-list')(params)
    },
    back: requestCurrying.post<{ folderIds: string[] }>('/app-api/creative/storage/directory/back'),
  },

  /**
   * 素材媒体资源
   */
  media: {
    list: (params: MaterialResource.MaterialMediaParams) => {
      return fetchPaginationGet<MaterialResource.Media>(
        '/app-api/creative/storage/media/page'
      )(params)
    },
    rename: requestCurrying.post<Pick<MaterialResource.Media, 'fileId' | 'fileName'>>('/app-api/creative/storage/media/rename'),
    delete: requestCurrying.post<{ fileIds: string[] }>('/app-api/creative/storage/media/delete'),
    recycle: requestCurrying.post<{ fileIds: string[] }>('/app-api/creative/storage/media/recycle'),
    recycleList: (params: MaterialResource.MaterialMediaParams) => {
      return fetchPaginationGet<MaterialResource.Media>('/app-api/creative/storage/media/recycle-page')(params)
    },
    move: requestCurrying.post<{ fileIds: string[], folderUuid: string }>('/app-api/creative/storage/media/move'),
    back: requestCurrying.post<{ fileIds: string[] }>('/app-api/creative/storage/media/back'),
    cover: (objectId: string) => OssModule.getObjectHref(objectId)
  },

  timbre: {
    list: (params: PaginationParams) => {
      return fetchPagination<TimbreResource.Timbre>('/app-api/creative/library/tts/search', params)
    }
  }
}
