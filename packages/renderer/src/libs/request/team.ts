import { TeamManager } from '../storage'
import { TeamAPI } from './api/team'

async function check(id: number | null) {
  if (!id) return false
  try {
    return await TeamAPI.check({ teamId: id })
  } catch {
    return false
  }
}

export async function refreshTeam() {
  let valid = await check(TeamManager.current())
  if (valid) return valid

  TeamManager.clear()
  const list = await TeamAPI.list({})
  if (!list.length) return false

  valid = await check(list[0].id)
  if (valid) TeamManager.switch(list[0].id)

  return valid
}
