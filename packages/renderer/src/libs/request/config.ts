/**
 * 请求配置
 */

// 基础 URL 配置
export const BASE_URL = 'http://47.99.131.55:48080'

// 超时时间（毫秒）
export const TIMEOUT = 10000

// 默认请求头
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  // BUG: 删除该测试字段
  'tenant-id': 1,
  // BUG: 删除该测试字段
  Authorization: 'test1'
}

// HTTP 状态码消息映射
export const HTTP_STATUS_MESSAGE: Record<number, string> = {
  400: '请求参数错误',
  401: '未授权，请重新登录',
  403: '拒绝访问',
  404: '请求地址不存在',
  405: '请求方法不允许',
  408: '请求超时',
  500: '服务器内部错误',
  501: '服务未实现',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时',
  505: 'HTTP版本不支持'
}

// 业务状态码消息映射
export const BUSINESS_CODE_MESSAGE: Record<number, string> = {
  10000: '操作成功',
  10001: '操作失败',
  10002: '参数错误',
  10003: '未授权',
  10004: '资源不存在',
  10005: '服务器内部错误'
}

// 请求重试配置
export const RETRY_CONFIG = {
  count: 3, // 重试次数
  delay: 1000 // 重试延迟（毫秒）
}

// 是否开启请求日志
export const ENABLE_REQUEST_LOG = false
